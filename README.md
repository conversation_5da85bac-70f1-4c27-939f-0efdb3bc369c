# FLUX.1 Kontext Pro Image Generator

A comprehensive, modern GUI application for generating images using the FLUX.1 Kontext Pro API. This application provides a complete rewrite with improved architecture, user-friendly interface, and robust error handling.

## Features

### 🎨 Core Functionality
- **FLUX.1 Kontext Pro API Integration**: Full support for the latest FLUX.1 Kontext Pro text-to-image API
- **Comprehensive Parameter Control**: All API parameters supported including:
  - Prompt input with quick prompt suggestions
  - Seed control with randomization
  - Guidance scale (0.0 - 20.0)
  - Number of images (1-10)
  - Safety tolerance levels (1-6)
  - Output formats (JPEG, PNG)
  - Aspect ratios (21:9, 16:9, 4:3, 3:2, 1:1, 2:3, 3:4, 9:16, 9:21)
- **Batch Image Generation**: Generate multiple images in a single request

### 🖥️ Modern GUI Interface
- **Responsive Design**: Clean, modern interface that works on different screen sizes
- **Real-time Progress Monitoring**: Live progress updates and status indicators
- **Image Preview**: Built-in image preview and management
- **Comprehensive Logging**: Detailed generation logs with timestamps
- **Settings Management**: Easy-to-use settings dialog for configuration

### 🔧 Advanced Features
- **Image Management System**: Automatic image downloading, caching, and organization
- **Thumbnail Generation**: Automatic thumbnail creation for quick preview
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Configuration Management**: Robust configuration system with validation
- **Logging System**: Advanced logging with multiple output targets

## Installation

### Prerequisites
- Python 3.8 or higher
- FAL API key (get one from [fal.ai](https://fal.ai))

### Setup
1. Clone or download the application files
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Run the application:
   ```bash
   python main.py
   ```

### Dependencies
- `fal-client>=0.4.0` - FLUX API client
- `aiohttp>=3.8.0` - Async HTTP client for image downloads
- `Pillow>=10.0.0` - Image processing and thumbnails

## Usage

### First Time Setup
1. Launch the application: `python main.py`
2. Click the "⚙️ Settings" button
3. Enter your FAL API key
4. Configure output directory and other preferences
5. Click "Save"

### Generating Images
1. Enter your image prompt in the text area
2. Adjust parameters as needed:
   - **Seed**: Leave empty for random, or enter a specific number for reproducible results
   - **Guidance Scale**: Higher values follow the prompt more closely (default: 3.5)
   - **Number of Images**: How many images to generate (1-10)
   - **Safety Tolerance**: Content filtering level (1=strict, 6=permissive)
   - **Output Format**: JPEG or PNG
   - **Aspect Ratio**: Choose from various aspect ratios
3. Click "🚀 Generate Images"
4. Monitor progress in the progress section
5. View generated images in the preview area
6. Images are automatically saved to your output directory

### Quick Prompts
Use the quick prompt buttons for inspiration:
- "A majestic landscape at sunset"
- "Portrait of a wise old wizard"
- "Futuristic city skyline"
- "Abstract art with vibrant colors"

## File Structure

```
flux-python/
├── main.py                 # Main application entry point
├── modern_gui.py          # Modern GUI implementation
├── generator.py           # FLUX API client and generation logic
├── settings.py            # Configuration management
├── image_manager.py       # Image handling and caching
├── logger.py              # Logging and error handling
├── test_application.py    # Test suite
├── requirements.txt       # Python dependencies
├── README.md             # This file
├── flux_config.json      # Configuration file (created automatically)
├── output/               # Generated images directory
│   ├── thumbnails/       # Image thumbnails
│   └── prompts/          # Saved prompts
└── flux_generator.log    # Application log file
```

## Configuration

The application uses a JSON configuration file (`flux_config.json`) that is created automatically on first run. You can also manually edit this file:

```json
{
    "api_key": "your-fal-api-key",
    "output_directory": "output",
    "model_endpoint": "fal-ai/flux-pro/kontext/text-to-image",
    "guidance_scale": 3.5,
    "num_images": 1,
    "safety_tolerance": "2",
    "output_format": "jpeg",
    "aspect_ratio": "1:1",
    "window_width": 1200,
    "window_height": 800,
    "auto_open_images": true,
    "auto_save_settings": true
}
```

## Command Line Options

```bash
python main.py [options]

Options:
  -h, --help            Show help message
  -c, --config CONFIG   Path to configuration file
  -d, --debug           Enable debug mode with console logging
  -v, --version         Show version information
```

Examples:
```bash
python main.py                    # Run with default settings
python main.py --debug            # Run with debug logging
python main.py --config my.json   # Use custom config file
```

## Testing

Run the test suite to validate all components:

```bash
python test_application.py
```

The test suite validates:
- Module imports
- Configuration management
- Generation request creation
- Image manager functionality
- Logging system
- Enum values

## Troubleshooting

### Common Issues

**"API Key Required" Error**
- Ensure you have entered a valid FAL API key in the settings
- Check that your API key has sufficient credits

**"Network Error"**
- Check your internet connection
- Verify firewall settings allow the application to access the internet
- Try disabling VPN if active

**"File Permission Error"**
- Ensure the application has write permissions to the output directory
- Try running as administrator on Windows
- Check available disk space

**GUI Not Appearing**
- Ensure tkinter is installed: `python -m tkinter`
- Update your graphics drivers
- Try running with `--debug` flag for more information

### Log Files
Check the log file (`flux_generator.log`) for detailed error information and debugging.

## API Information

This application uses the FLUX.1 Kontext Pro API endpoint:
- **Endpoint**: `fal-ai/flux-pro/kontext/text-to-image`
- **Documentation**: [FLUX.1 Kontext Pro API Docs](https://fal.ai/models/fal-ai/flux-pro/kontext/text-to-image)
- **API Key**: Required from [fal.ai](https://fal.ai)

## License

This project is provided as-is for educational and personal use. Please respect the FLUX API terms of service and usage guidelines.

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the log files for error details
3. Ensure all dependencies are properly installed
4. Verify your API key is valid and has credits

## Version History

**v1.0** - Initial release
- Complete rewrite of the original application
- Modern GUI with comprehensive parameter controls
- FLUX.1 Kontext Pro API integration
- Advanced image management and caching
- Robust error handling and logging
- Comprehensive test suite
