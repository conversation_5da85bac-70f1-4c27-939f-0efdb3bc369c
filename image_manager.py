"""
Image Management System for FLUX.1 Kontext Pro Image Generator
Handles image downloading, caching, preview, save/export functionality with proper file management.
"""

import asyncio
import logging
import os
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from urllib.parse import urlparse
import hashlib

import aiohttp
from PIL import Image, ImageTk
import tkinter as tk


@dataclass
class ImageInfo:
    """Information about a generated image."""
    url: str
    local_path: Optional[str] = None
    filename: str = ""
    content_type: str = "image/jpeg"
    file_size: int = 0
    width: int = 0
    height: int = 0
    prompt: str = ""
    seed: Optional[int] = None
    timestamp: str = ""
    thumbnail_path: Optional[str] = None


class ImageCache:
    """Simple image cache for managing downloaded images."""
    
    def __init__(self, cache_dir: str = "cache", max_size_mb: int = 500):
        """
        Initialize the image cache.
        
        Args:
            cache_dir: Directory for cached images.
            max_size_mb: Maximum cache size in megabytes.
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.cache_index = {}  # url -> ImageInfo
        self.logger = logging.getLogger(__name__)
    
    def _get_cache_key(self, url: str) -> str:
        """Generate a cache key from URL."""
        return hashlib.md5(url.encode()).hexdigest()
    
    def get(self, url: str) -> Optional[ImageInfo]:
        """Get cached image info by URL."""
        return self.cache_index.get(url)
    
    def put(self, url: str, image_info: ImageInfo) -> None:
        """Add image info to cache."""
        self.cache_index[url] = image_info
        self._cleanup_if_needed()
    
    def _cleanup_if_needed(self) -> None:
        """Clean up cache if it exceeds size limit."""
        total_size = sum(
            Path(info.local_path).stat().st_size 
            for info in self.cache_index.values() 
            if info.local_path and Path(info.local_path).exists()
        )
        
        if total_size > self.max_size_bytes:
            # Remove oldest files first
            sorted_items = sorted(
                self.cache_index.items(),
                key=lambda x: Path(x[1].local_path).stat().st_mtime if x[1].local_path else 0
            )
            
            for url, info in sorted_items:
                if info.local_path and Path(info.local_path).exists():
                    try:
                        Path(info.local_path).unlink()
                        if info.thumbnail_path and Path(info.thumbnail_path).exists():
                            Path(info.thumbnail_path).unlink()
                        del self.cache_index[url]
                        self.logger.info(f"Removed cached image: {info.filename}")
                        
                        # Check if we're under the limit now
                        total_size -= Path(info.local_path).stat().st_size
                        if total_size <= self.max_size_bytes * 0.8:  # 80% threshold
                            break
                    except Exception as e:
                        self.logger.error(f"Error removing cached file: {e}")


class ImageManager:
    """
    Comprehensive image management system for the FLUX.1 Kontext Pro Image Generator.
    Handles downloading, caching, thumbnails, and file operations.
    """
    
    def __init__(self, output_dir: str = "output", cache_dir: str = "cache"):
        """
        Initialize the image manager.
        
        Args:
            output_dir: Directory for saved images.
            cache_dir: Directory for cached images.
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.cache = ImageCache(cache_dir)
        self.logger = logging.getLogger(__name__)
        
        # Create subdirectories
        (self.output_dir / "thumbnails").mkdir(exist_ok=True)
        (self.output_dir / "prompts").mkdir(exist_ok=True)
    
    async def download_image(self, url: str, prompt: str = "", seed: Optional[int] = None,
                           session: Optional[aiohttp.ClientSession] = None) -> ImageInfo:
        """
        Download an image from URL and create ImageInfo.
        
        Args:
            url: Image URL to download.
            prompt: The prompt used to generate the image.
            seed: The seed used for generation.
            session: Optional aiohttp session to use.
            
        Returns:
            ImageInfo object with download details.
        """
        # Check cache first
        cached = self.cache.get(url)
        if cached and cached.local_path and Path(cached.local_path).exists():
            self.logger.info(f"Using cached image: {cached.filename}")
            return cached
        
        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        parsed_url = urlparse(url)
        extension = Path(parsed_url.path).suffix or ".jpg"
        
        seed_str = f"seed_{seed}" if seed is not None else "no_seed"
        filename = f"{timestamp}_{seed_str}{extension}"
        local_path = self.output_dir / filename
        
        # Download the image
        close_session = False
        if session is None:
            session = aiohttp.ClientSession()
            close_session = True
        
        try:
            async with session.get(url) as response:
                if response.status == 200:
                    content = await response.read()
                    
                    # Save to file
                    with open(local_path, 'wb') as f:
                        f.write(content)
                    
                    # Get image dimensions
                    try:
                        with Image.open(local_path) as img:
                            width, height = img.size
                    except Exception as e:
                        self.logger.warning(f"Could not get image dimensions: {e}")
                        width = height = 0
                    
                    # Create ImageInfo
                    image_info = ImageInfo(
                        url=url,
                        local_path=str(local_path),
                        filename=filename,
                        content_type=response.headers.get('content-type', 'image/jpeg'),
                        file_size=len(content),
                        width=width,
                        height=height,
                        prompt=prompt,
                        seed=seed,
                        timestamp=timestamp
                    )
                    
                    # Create thumbnail
                    await self._create_thumbnail(image_info)
                    
                    # Save prompt if provided
                    if prompt:
                        await self._save_prompt(prompt, seed, timestamp)
                    
                    # Cache the image info
                    self.cache.put(url, image_info)
                    
                    self.logger.info(f"Downloaded image: {filename}")
                    return image_info
                
                else:
                    raise Exception(f"Failed to download image: HTTP {response.status}")
        
        finally:
            if close_session:
                await session.close()
    
    async def download_images_batch(self, urls: List[str], prompt: str = "", 
                                  seed: Optional[int] = None) -> List[ImageInfo]:
        """
        Download multiple images concurrently.
        
        Args:
            urls: List of image URLs to download.
            prompt: The prompt used to generate the images.
            seed: The seed used for generation.
            
        Returns:
            List of ImageInfo objects.
        """
        async with aiohttp.ClientSession() as session:
            tasks = [
                self.download_image(url, prompt, seed, session)
                for url in urls
            ]
            return await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _create_thumbnail(self, image_info: ImageInfo, size: Tuple[int, int] = (200, 200)) -> None:
        """Create a thumbnail for the image."""
        if not image_info.local_path:
            return
        
        try:
            thumbnail_path = self.output_dir / "thumbnails" / f"thumb_{image_info.filename}"
            
            with Image.open(image_info.local_path) as img:
                img.thumbnail(size, Image.Resampling.LANCZOS)
                img.save(thumbnail_path, "JPEG", quality=85)
            
            image_info.thumbnail_path = str(thumbnail_path)
            self.logger.debug(f"Created thumbnail: {thumbnail_path}")
            
        except Exception as e:
            self.logger.error(f"Error creating thumbnail: {e}")
    
    async def _save_prompt(self, prompt: str, seed: Optional[int], timestamp: str) -> None:
        """Save the prompt to a text file."""
        try:
            seed_str = f"seed_{seed}" if seed is not None else "no_seed"
            prompt_filename = f"{timestamp}_{seed_str}_prompt.txt"
            prompt_path = self.output_dir / "prompts" / prompt_filename
            
            with open(prompt_path, 'w', encoding='utf-8') as f:
                f.write(f"Timestamp: {timestamp}\n")
                f.write(f"Seed: {seed}\n")
                f.write(f"Prompt: {prompt}\n")
            
            self.logger.debug(f"Saved prompt: {prompt_filename}")
            
        except Exception as e:
            self.logger.error(f"Error saving prompt: {e}")
    
    def get_thumbnail_for_display(self, image_info: ImageInfo, size: Tuple[int, int] = (150, 150)) -> Optional[ImageTk.PhotoImage]:
        """
        Get a PhotoImage suitable for tkinter display.
        
        Args:
            image_info: ImageInfo object.
            size: Desired thumbnail size.
            
        Returns:
            PhotoImage object or None if error.
        """
        try:
            if image_info.thumbnail_path and Path(image_info.thumbnail_path).exists():
                img_path = image_info.thumbnail_path
            elif image_info.local_path and Path(image_info.local_path).exists():
                img_path = image_info.local_path
            else:
                return None
            
            with Image.open(img_path) as img:
                img.thumbnail(size, Image.Resampling.LANCZOS)
                return ImageTk.PhotoImage(img)
        
        except Exception as e:
            self.logger.error(f"Error creating display thumbnail: {e}")
            return None
    
    def export_image(self, image_info: ImageInfo, destination: str) -> bool:
        """
        Export/copy an image to a destination path.
        
        Args:
            image_info: ImageInfo object.
            destination: Destination file path.
            
        Returns:
            True if successful, False otherwise.
        """
        try:
            if not image_info.local_path or not Path(image_info.local_path).exists():
                self.logger.error("Source image file not found")
                return False
            
            dest_path = Path(destination)
            dest_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(image_info.local_path, dest_path)
            self.logger.info(f"Exported image to: {destination}")
            return True
        
        except Exception as e:
            self.logger.error(f"Error exporting image: {e}")
            return False
    
    def get_recent_images(self, limit: int = 20) -> List[ImageInfo]:
        """
        Get recently downloaded images.
        
        Args:
            limit: Maximum number of images to return.
            
        Returns:
            List of ImageInfo objects sorted by timestamp (newest first).
        """
        images = list(self.cache.cache_index.values())
        images.sort(key=lambda x: x.timestamp, reverse=True)
        return images[:limit]
    
    def cleanup_old_files(self, days: int = 30) -> int:
        """
        Clean up files older than specified days.
        
        Args:
            days: Number of days to keep files.
            
        Returns:
            Number of files cleaned up.
        """
        cutoff_time = datetime.now().timestamp() - (days * 24 * 60 * 60)
        cleaned_count = 0
        
        for image_info in list(self.cache.cache_index.values()):
            if image_info.local_path:
                try:
                    file_path = Path(image_info.local_path)
                    if file_path.exists() and file_path.stat().st_mtime < cutoff_time:
                        file_path.unlink()
                        if image_info.thumbnail_path:
                            Path(image_info.thumbnail_path).unlink(missing_ok=True)
                        
                        # Remove from cache
                        for url, info in list(self.cache.cache_index.items()):
                            if info == image_info:
                                del self.cache.cache_index[url]
                                break
                        
                        cleaned_count += 1
                        self.logger.info(f"Cleaned up old file: {image_info.filename}")
                
                except Exception as e:
                    self.logger.error(f"Error cleaning up file: {e}")
        
        return cleaned_count
