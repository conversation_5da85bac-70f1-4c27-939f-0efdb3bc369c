"""
Modern GUI for FLUX.1 Kontext Pro Image Generator
A comprehensive, user-friendly interface with all FLUX.1 Kontext Pro parameters and features.
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, filedialog, messagebox
import threading
import asyncio
from datetime import datetime
from typing import Optional, Callable, List
import logging

from generator import FluxKontextClient, GenerationRequest, SafetyTolerance, OutputFormat, AspectRatio
from settings import ConfigManager
from image_manager import ImageManager


class ModernFluxGUI(tk.Tk):
    """
    Modern GUI application for FLUX.1 Kontext Pro Image Generator.
    Features comprehensive parameter controls, image preview, and progress monitoring.
    """
    
    def __init__(self):
        """Initialize the modern GUI application."""
        super().__init__()
        
        # Initialize core components
        self.config_manager = ConfigManager()
        self.image_manager = ImageManager()
        self.flux_client = None
        self.current_generation_task = None
        
        # Setup window
        self.setup_window()
        self.setup_styles()
        
        # Create GUI components
        self.create_widgets()
        
        # Initialize client
        self.initialize_client()
        
        # Center window
        self.center_window()
    
    def setup_window(self):
        """Setup main window properties."""
        config = self.config_manager.get_config()
        
        self.title("🎨 FLUX.1 Kontext Pro Image Generator")
        self.geometry(f"{config.window_width}x{config.window_height}")
        self.minsize(1000, 700)
        
        # Modern color scheme
        self.colors = {
            'bg': '#f8f9fa',
            'card': '#ffffff',
            'primary': '#0066cc',
            'primary_hover': '#0052a3',
            'secondary': '#6c757d',
            'success': '#28a745',
            'warning': '#ffc107',
            'danger': '#dc3545',
            'text': '#212529',
            'text_muted': '#6c757d',
            'border': '#dee2e6',
            'input_bg': '#ffffff',
            'input_border': '#ced4da'
        }
        
        self.configure(bg=self.colors['bg'])
    
    def setup_styles(self):
        """Setup modern ttk styles."""
        style = ttk.Style()
        
        # Configure modern button style
        style.configure(
            'Modern.TButton',
            padding=(12, 8),
            font=('Segoe UI', 10),
            borderwidth=1,
            focuscolor='none'
        )
        
        # Configure primary button style
        style.configure(
            'Primary.TButton',
            padding=(15, 10),
            font=('Segoe UI', 10, 'bold'),
            borderwidth=0,
            focuscolor='none'
        )
        
        # Configure frame styles
        style.configure(
            'Card.TFrame',
            background=self.colors['card'],
            borderwidth=1,
            relief='solid'
        )
        
        # Configure label styles
        style.configure(
            'Heading.TLabel',
            font=('Segoe UI', 12, 'bold'),
            background=self.colors['card'],
            foreground=self.colors['text']
        )
        
        style.configure(
            'Card.TLabel',
            background=self.colors['card'],
            foreground=self.colors['text'],
            font=('Segoe UI', 9)
        )
    
    def create_widgets(self):
        """Create and layout all GUI widgets."""
        # Main container with padding
        main_container = ttk.Frame(self)
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Create main sections
        self.create_header(main_container)
        self.create_main_content(main_container)
        self.create_status_bar(main_container)
    
    def create_header(self, parent):
        """Create the header section."""
        header_frame = ttk.Frame(parent, style='Card.TFrame')
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Title and API status
        title_frame = ttk.Frame(header_frame, style='Card.TFrame')
        title_frame.pack(fill=tk.X, padx=20, pady=15)
        
        title_label = ttk.Label(
            title_frame,
            text="FLUX.1 Kontext Pro Image Generator",
            style='Heading.TLabel',
            font=('Segoe UI', 16, 'bold')
        )
        title_label.pack(side=tk.LEFT)
        
        # API status indicator
        self.api_status_label = ttk.Label(
            title_frame,
            text="🔴 API Not Connected",
            style='Card.TLabel',
            font=('Segoe UI', 9)
        )
        self.api_status_label.pack(side=tk.RIGHT)
        
        # Settings button
        settings_btn = ttk.Button(
            title_frame,
            text="⚙️ Settings",
            style='Modern.TButton',
            command=self.open_settings
        )
        settings_btn.pack(side=tk.RIGHT, padx=(0, 10))
    
    def create_main_content(self, parent):
        """Create the main content area."""
        # Create paned window for resizable sections
        paned_window = ttk.PanedWindow(parent, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        
        # Left panel - Controls
        left_panel = ttk.Frame(paned_window, style='Card.TFrame')
        paned_window.add(left_panel, weight=1)
        
        # Right panel - Output and Images
        right_panel = ttk.Frame(paned_window, style='Card.TFrame')
        paned_window.add(right_panel, weight=1)
        
        # Create control sections
        self.create_prompt_section(left_panel)
        self.create_parameters_section(left_panel)
        self.create_generation_controls(left_panel)
        
        # Create output sections
        self.create_progress_section(right_panel)
        self.create_image_preview_section(right_panel)
        self.create_log_section(right_panel)
    
    def create_prompt_section(self, parent):
        """Create the prompt input section."""
        prompt_frame = ttk.LabelFrame(parent, text="Image Prompt", padding=15)
        prompt_frame.pack(fill=tk.X, padx=15, pady=(15, 10))
        
        # Prompt text area
        self.prompt_text = tk.Text(
            prompt_frame,
            height=4,
            wrap=tk.WORD,
            font=('Segoe UI', 10),
            bg=self.colors['input_bg'],
            fg=self.colors['text'],
            borderwidth=1,
            relief='solid',
            highlightthickness=1,
            highlightcolor=self.colors['primary']
        )
        self.prompt_text.pack(fill=tk.X, pady=(0, 10))
        
        # Quick prompt buttons
        quick_prompts_frame = ttk.Frame(prompt_frame)
        quick_prompts_frame.pack(fill=tk.X)
        
        quick_prompts = [
            "A majestic landscape at sunset",
            "Portrait of a wise old wizard",
            "Futuristic city skyline",
            "Abstract art with vibrant colors"
        ]
        
        for i, prompt in enumerate(quick_prompts):
            btn = ttk.Button(
                quick_prompts_frame,
                text=prompt[:20] + "...",
                style='Modern.TButton',
                command=lambda p=prompt: self.set_prompt(p)
            )
            btn.pack(side=tk.LEFT, padx=(0, 5) if i < len(quick_prompts)-1 else 0)
    
    def create_parameters_section(self, parent):
        """Create the parameters control section."""
        params_frame = ttk.LabelFrame(parent, text="Generation Parameters", padding=15)
        params_frame.pack(fill=tk.X, padx=15, pady=10)
        
        # Create parameter controls in a grid
        self.create_parameter_controls(params_frame)
    
    def create_parameter_controls(self, parent):
        """Create individual parameter controls."""
        # Seed control
        seed_frame = ttk.Frame(parent)
        seed_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(seed_frame, text="Seed:", width=15).pack(side=tk.LEFT)
        self.seed_var = tk.StringVar()
        seed_entry = ttk.Entry(seed_frame, textvariable=self.seed_var, width=15)
        seed_entry.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Button(
            seed_frame,
            text="Random",
            style='Modern.TButton',
            command=self.randomize_seed
        ).pack(side=tk.LEFT)
        
        # Guidance Scale
        guidance_frame = ttk.Frame(parent)
        guidance_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(guidance_frame, text="Guidance Scale:", width=15).pack(side=tk.LEFT)
        self.guidance_var = tk.DoubleVar(value=3.5)
        guidance_scale = ttk.Scale(
            guidance_frame,
            from_=0.0,
            to=20.0,
            variable=self.guidance_var,
            orient=tk.HORIZONTAL,
            length=200
        )
        guidance_scale.pack(side=tk.LEFT, padx=5)
        
        self.guidance_label = ttk.Label(guidance_frame, text="3.5")
        self.guidance_label.pack(side=tk.LEFT, padx=5)
        guidance_scale.configure(command=self.update_guidance_label)
        
        # Number of Images
        num_images_frame = ttk.Frame(parent)
        num_images_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(num_images_frame, text="Number of Images:", width=15).pack(side=tk.LEFT)
        self.num_images_var = tk.IntVar(value=1)
        num_images_spin = ttk.Spinbox(
            num_images_frame,
            from_=1,
            to=10,
            textvariable=self.num_images_var,
            width=10
        )
        num_images_spin.pack(side=tk.LEFT, padx=5)
        
        # Safety Tolerance
        safety_frame = ttk.Frame(parent)
        safety_frame.pack(fill=tk.X, pady=5)

        ttk.Label(safety_frame, text="Safety Tolerance:", width=15).pack(side=tk.LEFT)
        self.safety_var = tk.StringVar(value="6")  # Default to maximum permissive
        safety_combo = ttk.Combobox(
            safety_frame,
            textvariable=self.safety_var,
            values=[e.value for e in SafetyTolerance],
            state="readonly",
            width=12
        )
        safety_combo.pack(side=tk.LEFT, padx=5)

        # Output Format
        format_frame = ttk.Frame(parent)
        format_frame.pack(fill=tk.X, pady=5)

        ttk.Label(format_frame, text="Output Format:", width=15).pack(side=tk.LEFT)
        self.format_var = tk.StringVar(value="png")  # Default to PNG
        format_combo = ttk.Combobox(
            format_frame,
            textvariable=self.format_var,
            values=[e.value for e in OutputFormat],
            state="readonly",
            width=12
        )
        format_combo.pack(side=tk.LEFT, padx=5)
        
        # Aspect Ratio
        aspect_frame = ttk.Frame(parent)
        aspect_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(aspect_frame, text="Aspect Ratio:", width=15).pack(side=tk.LEFT)
        self.aspect_var = tk.StringVar(value="1:1")
        aspect_combo = ttk.Combobox(
            aspect_frame,
            textvariable=self.aspect_var,
            values=[e.value for e in AspectRatio],
            state="readonly",
            width=12
        )
        aspect_combo.pack(side=tk.LEFT, padx=5)
    
    def create_generation_controls(self, parent):
        """Create generation control buttons."""
        controls_frame = ttk.Frame(parent)
        controls_frame.pack(fill=tk.X, padx=15, pady=15)
        
        # Generate button
        self.generate_btn = ttk.Button(
            controls_frame,
            text="🚀 Generate Images",
            style='Primary.TButton',
            command=self.start_generation
        )
        self.generate_btn.pack(fill=tk.X, pady=(0, 10))
        
        # Cancel button
        self.cancel_btn = ttk.Button(
            controls_frame,
            text="❌ Cancel Generation",
            style='Modern.TButton',
            command=self.cancel_generation,
            state=tk.DISABLED
        )
        self.cancel_btn.pack(fill=tk.X)
    
    def create_progress_section(self, parent):
        """Create the progress monitoring section."""
        progress_frame = ttk.LabelFrame(parent, text="Generation Progress", padding=15)
        progress_frame.pack(fill=tk.X, padx=15, pady=(15, 10))
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            mode='indeterminate'
        )
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))
        
        # Status label
        self.status_label = ttk.Label(
            progress_frame,
            text="Ready to generate images",
            font=('Segoe UI', 9)
        )
        self.status_label.pack()
    
    def create_image_preview_section(self, parent):
        """Create the image preview section."""
        preview_frame = ttk.LabelFrame(parent, text="Generated Images", padding=15)
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # Create scrollable frame for images
        canvas = tk.Canvas(preview_frame, bg=self.colors['card'])
        scrollbar = ttk.Scrollbar(preview_frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Initial placeholder
        self.preview_placeholder = ttk.Label(
            self.scrollable_frame,
            text="Generated images will appear here",
            font=('Segoe UI', 10),
            foreground=self.colors['text_muted']
        )
        self.preview_placeholder.pack(expand=True, pady=50)

        # Store references
        self.preview_canvas = canvas
        self.preview_scrollbar = scrollbar
        self.image_widgets = []  # Store image widget references
    
    def create_log_section(self, parent):
        """Create the log output section."""
        log_frame = ttk.LabelFrame(parent, text="Generation Log", padding=10)
        log_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        # Log text area
        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            height=8,
            wrap=tk.WORD,
            font=('Consolas', 9),
            bg='#f8f9fa',
            fg=self.colors['text'],
            borderwidth=1,
            relief='solid'
        )
        self.log_text.pack(fill=tk.X)
    
    def create_status_bar(self, parent):
        """Create the status bar."""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.status_bar_label = ttk.Label(
            status_frame,
            text="Ready",
            font=('Segoe UI', 9),
            foreground=self.colors['text_muted']
        )
        self.status_bar_label.pack(side=tk.LEFT)
        
        # Version info
        version_label = ttk.Label(
            status_frame,
            text="FLUX.1 Kontext Pro v1.0",
            font=('Segoe UI', 9),
            foreground=self.colors['text_muted']
        )
        version_label.pack(side=tk.RIGHT)

    def initialize_client(self):
        """Initialize the FLUX client with callbacks."""
        try:
            config = self.config_manager.get_config()

            self.flux_client = FluxKontextClient(
                api_key=config.api_key,
                progress_callback=self.update_progress,
                log_callback=self.log_message
            )

            if config.api_key:
                self.api_status_label.config(text="🟢 API Connected")
                self.log_message("FLUX.1 Kontext Pro client initialized successfully")
            else:
                self.api_status_label.config(text="🟡 API Key Required")
                self.log_message("Warning: No API key configured. Please check settings.")

        except Exception as e:
            self.api_status_label.config(text="🔴 API Error")
            self.log_message(f"Error initializing client: {str(e)}")

    def center_window(self):
        """Center the window on screen."""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def set_prompt(self, prompt: str):
        """Set the prompt text."""
        self.prompt_text.delete(1.0, tk.END)
        self.prompt_text.insert(1.0, prompt)

    def randomize_seed(self):
        """Generate a random seed."""
        import random
        seed = random.randint(0, 2**32 - 1)
        self.seed_var.set(str(seed))

    def update_guidance_label(self, value):
        """Update the guidance scale label."""
        self.guidance_label.config(text=f"{float(value):.1f}")

    def log_message(self, message: str):
        """Add a message to the log."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.update_idletasks()

    def update_progress(self, message: str):
        """Update the progress status."""
        self.status_label.config(text=message)
        self.status_bar_label.config(text=message)
        self.update_idletasks()

    def start_generation(self):
        """Start the image generation process."""
        # Validate inputs
        prompt = self.prompt_text.get(1.0, tk.END).strip()
        if not prompt:
            messagebox.showwarning("Warning", "Please enter a prompt for image generation.")
            return

        if not self.flux_client:
            messagebox.showerror("Error", "FLUX client not initialized. Please check your API key.")
            return

        # Prepare generation request
        try:
            seed_str = self.seed_var.get().strip()
            seed = int(seed_str) if seed_str and seed_str.isdigit() else None

            request = GenerationRequest(
                prompt=prompt,
                seed=seed,
                guidance_scale=self.guidance_var.get(),
                num_images=self.num_images_var.get(),
                safety_tolerance=SafetyTolerance(self.safety_var.get()),
                output_format=OutputFormat(self.format_var.get()),
                aspect_ratio=AspectRatio(self.aspect_var.get())
            )

            # Update UI state
            self.generate_btn.config(state=tk.DISABLED)
            self.cancel_btn.config(state=tk.NORMAL)
            self.progress_bar.start(10)

            # Start generation in background thread
            self.current_generation_task = threading.Thread(
                target=self.run_generation,
                args=(request,),
                daemon=True
            )
            self.current_generation_task.start()

            self.log_message(f"Starting generation: '{prompt[:50]}...'")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to start generation: {str(e)}")
            self.generation_complete()

    def run_generation(self, request: GenerationRequest):
        """Run the generation process in a background thread."""
        try:
            # Generate images
            result = self.flux_client.generate(request)

            # Download images
            if result.images:
                image_urls = [img.get('url') for img in result.images if img.get('url')]

                # Use asyncio to download images
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                downloaded_images = loop.run_until_complete(
                    self.image_manager.download_images_batch(
                        image_urls,
                        request.prompt,
                        request.seed
                    )
                )

                loop.close()

                # Update UI on main thread
                self.after(0, lambda: self.generation_success(downloaded_images))
            else:
                self.after(0, lambda: self.generation_error("No images were generated"))

        except Exception as e:
            self.after(0, lambda: self.generation_error(str(e)))

    def generation_success(self, images):
        """Handle successful generation."""
        self.log_message(f"Generation completed! Downloaded {len(images)} images.")

        # Display images in preview
        if images:
            self.display_images(images)

        self.generation_complete()

    def display_images(self, images):
        """Display generated images in the preview section."""
        try:
            self.log_message(f"Attempting to display {len(images)} images")

            # Clear existing images
            self.clear_image_preview()

            # Hide placeholder
            if hasattr(self, 'preview_placeholder'):
                self.preview_placeholder.pack_forget()

            # Display each image
            valid_images = 0
            for i, image_info in enumerate(images):
                self.log_message(f"Processing image {i+1}: {type(image_info)}")

                if isinstance(image_info, Exception):
                    self.log_message(f"Error downloading image {i+1}: {str(image_info)}")
                    continue

                # Check if it's a valid ImageInfo object
                if hasattr(image_info, 'local_path') and image_info.local_path:
                    self.log_message(f"Adding image {i+1} to preview: {image_info.local_path}")
                    self.add_image_to_preview(image_info, i)
                    valid_images += 1
                else:
                    self.log_message(f"Image {i+1} has no local path or is invalid")

            if valid_images == 0:
                # Show a message if no valid images
                no_images_label = ttk.Label(
                    self.scrollable_frame,
                    text="No images could be displayed. Check the log for details.",
                    font=('Segoe UI', 10),
                    foreground=self.colors['text_muted']
                )
                no_images_label.pack(expand=True, pady=50)
                self.image_widgets.append(no_images_label)

            # Update scroll region
            self.scrollable_frame.update_idletasks()
            self.preview_canvas.configure(scrollregion=self.preview_canvas.bbox("all"))

            self.log_message(f"Successfully displayed {valid_images} out of {len(images)} images")

        except Exception as e:
            self.log_message(f"Error displaying images: {str(e)}")
            import traceback
            self.log_message(f"Traceback: {traceback.format_exc()}")

    def add_image_to_preview(self, image_info, index):
        """Add a single image to the preview section."""
        try:
            self.log_message(f"Creating preview for image {index + 1}: {getattr(image_info, 'filename', 'Unknown')}")

            # Create frame for this image
            image_frame = ttk.Frame(self.scrollable_frame, style='Card.TFrame')
            image_frame.pack(fill=tk.X, padx=5, pady=5)

            # Get thumbnail for display
            self.log_message(f"Getting thumbnail for: {getattr(image_info, 'local_path', 'No path')}")
            thumbnail = self.image_manager.get_thumbnail_for_display(image_info, size=(200, 200))

            if thumbnail:
                self.log_message(f"Thumbnail created successfully for image {index + 1}")
                # Image display
                image_label = ttk.Label(image_frame, image=thumbnail)
                image_label.pack(side=tk.LEFT, padx=10, pady=10)

                # Keep reference to prevent garbage collection
                image_label.image = thumbnail

                # Info panel
                info_frame = ttk.Frame(image_frame)
                info_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)

                # Image info
                ttk.Label(info_frame, text=f"Image {index + 1}",
                         font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

                if hasattr(image_info, 'filename'):
                    ttk.Label(info_frame, text=f"File: {image_info.filename}",
                             font=('Segoe UI', 9)).pack(anchor=tk.W)

                if hasattr(image_info, 'width') and hasattr(image_info, 'height'):
                    ttk.Label(info_frame, text=f"Size: {image_info.width}x{image_info.height}",
                             font=('Segoe UI', 9)).pack(anchor=tk.W)

                if hasattr(image_info, 'seed') and image_info.seed:
                    ttk.Label(info_frame, text=f"Seed: {image_info.seed}",
                             font=('Segoe UI', 9)).pack(anchor=tk.W)

                # Buttons
                button_frame = ttk.Frame(info_frame)
                button_frame.pack(anchor=tk.W, pady=(10, 0))

                # Open button
                ttk.Button(button_frame, text="📂 Open",
                          command=lambda: self.open_image(image_info),
                          style='Modern.TButton').pack(side=tk.LEFT, padx=(0, 5))

                # Export button
                ttk.Button(button_frame, text="💾 Export",
                          command=lambda: self.export_image(image_info),
                          style='Modern.TButton').pack(side=tk.LEFT)

                # Store widget reference
                self.image_widgets.append(image_frame)

            else:
                # Fallback if thumbnail creation failed
                self.log_message(f"Thumbnail creation failed for image {index + 1}, showing text fallback")
                ttk.Label(image_frame, text=f"Image {index + 1}: {getattr(image_info, 'filename', 'Unknown')}",
                         font=('Segoe UI', 10)).pack(padx=10, pady=10)
                self.image_widgets.append(image_frame)

        except Exception as e:
            self.log_message(f"Error adding image {index + 1} to preview: {str(e)}")
            import traceback
            self.log_message(f"Traceback: {traceback.format_exc()}")

    def clear_image_preview(self):
        """Clear all images from the preview section."""
        for widget in self.image_widgets:
            widget.destroy()
        self.image_widgets.clear()

    def open_image(self, image_info):
        """Open an image in the default system viewer."""
        try:
            import os
            import subprocess
            import platform

            if hasattr(image_info, 'local_path') and image_info.local_path:
                if platform.system() == 'Darwin':  # macOS
                    subprocess.call(['open', image_info.local_path])
                elif platform.system() == 'Windows':  # Windows
                    os.startfile(image_info.local_path)
                else:  # Linux
                    subprocess.call(['xdg-open', image_info.local_path])

                self.log_message(f"Opened image: {image_info.filename}")
            else:
                messagebox.showerror("Error", "Image file not found")

        except Exception as e:
            self.log_message(f"Error opening image: {str(e)}")
            messagebox.showerror("Error", f"Could not open image: {str(e)}")

    def export_image(self, image_info):
        """Export an image to a chosen location."""
        try:
            if not hasattr(image_info, 'local_path') or not image_info.local_path:
                messagebox.showerror("Error", "Image file not found")
                return

            # Get export destination
            default_filename = getattr(image_info, 'filename', 'image.jpg')
            filename = filedialog.asksaveasfilename(
                defaultextension=".png",
                filetypes=[
                    ("PNG files", "*.png"),
                    ("JPEG files", "*.jpg"),
                    ("All files", "*.*")
                ],
                title="Export image as...",
                initialfile=default_filename
            )

            if filename:
                success = self.image_manager.export_image(image_info, filename)
                if success:
                    self.log_message(f"Image exported to: {filename}")
                    messagebox.showinfo("Success", f"Image exported successfully to:\n{filename}")
                else:
                    messagebox.showerror("Error", "Failed to export image")

        except Exception as e:
            self.log_message(f"Error exporting image: {str(e)}")
            messagebox.showerror("Error", f"Could not export image: {str(e)}")

    def generation_error(self, error_message: str):
        """Handle generation error."""
        self.log_message(f"Generation failed: {error_message}")
        messagebox.showerror("Generation Error", f"Failed to generate images:\n{error_message}")
        self.generation_complete()

    def generation_complete(self):
        """Reset UI after generation completion."""
        self.generate_btn.config(state=tk.NORMAL)
        self.cancel_btn.config(state=tk.DISABLED)
        self.progress_bar.stop()
        self.update_progress("Ready to generate images")
        self.current_generation_task = None

    def cancel_generation(self):
        """Cancel the current generation."""
        if self.current_generation_task and self.current_generation_task.is_alive():
            self.log_message("Generation cancelled by user")
            # Note: Actual cancellation would require more complex thread management
            self.generation_complete()

    def open_settings(self):
        """Open the settings dialog."""
        SettingsDialog(self, self.config_manager)


class SettingsDialog(tk.Toplevel):
    """Settings dialog for configuring the application."""

    def __init__(self, parent, config_manager: ConfigManager):
        super().__init__(parent)
        self.parent = parent
        self.config_manager = config_manager
        self.config = config_manager.get_config()

        self.setup_dialog()
        self.create_widgets()
        self.load_current_settings()

    def setup_dialog(self):
        """Setup the dialog window."""
        self.title("Settings")
        self.geometry("500x400")
        self.resizable(False, False)
        self.transient(self.parent)
        self.grab_set()

        # Center on parent
        self.geometry(f"+{self.parent.winfo_x() + 50}+{self.parent.winfo_y() + 50}")

    def create_widgets(self):
        """Create settings widgets."""
        # Main frame
        main_frame = ttk.Frame(self, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # API Key section
        api_frame = ttk.LabelFrame(main_frame, text="API Configuration", padding=15)
        api_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(api_frame, text="FAL API Key:").pack(anchor=tk.W)
        self.api_key_var = tk.StringVar()
        api_entry = ttk.Entry(api_frame, textvariable=self.api_key_var, show="*", width=50)
        api_entry.pack(fill=tk.X, pady=(5, 0))

        # Output settings
        output_frame = ttk.LabelFrame(main_frame, text="Output Settings", padding=15)
        output_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(output_frame, text="Output Directory:").pack(anchor=tk.W)
        dir_frame = ttk.Frame(output_frame)
        dir_frame.pack(fill=tk.X, pady=(5, 10))

        self.output_dir_var = tk.StringVar()
        ttk.Entry(dir_frame, textvariable=self.output_dir_var, width=40).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(dir_frame, text="Browse", command=self.browse_directory).pack(side=tk.RIGHT, padx=(5, 0))

        # Auto-open images checkbox
        self.auto_open_var = tk.BooleanVar()
        ttk.Checkbutton(output_frame, text="Auto-open generated images", variable=self.auto_open_var).pack(anchor=tk.W)

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(15, 0))

        ttk.Button(button_frame, text="Cancel", command=self.destroy).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="Save", command=self.save_settings).pack(side=tk.RIGHT, padx=(0, 10))

    def load_current_settings(self):
        """Load current settings into the dialog."""
        self.api_key_var.set(self.config.api_key)
        self.output_dir_var.set(self.config.output_directory)
        self.auto_open_var.set(self.config.auto_open_images)

    def browse_directory(self):
        """Browse for output directory."""
        directory = filedialog.askdirectory(initialdir=self.output_dir_var.get())
        if directory:
            self.output_dir_var.set(directory)

    def save_settings(self):
        """Save the settings."""
        try:
            # Update configuration
            self.config_manager.update_config(
                api_key=self.api_key_var.get(),
                output_directory=self.output_dir_var.get(),
                auto_open_images=self.auto_open_var.get()
            )

            # Reinitialize parent's client
            self.parent.initialize_client()

            messagebox.showinfo("Settings", "Settings saved successfully!")
            self.destroy()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings: {str(e)}")


if __name__ == "__main__":
    app = ModernFluxGUI()
    app.mainloop()
