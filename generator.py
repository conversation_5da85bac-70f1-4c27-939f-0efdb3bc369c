import asyncio
import logging
from datetime import datetime

import fal_client
from aiohttp import ClientSession

from downloader import download_image
from utils import save_prompt


async def _generate_with_submit(prompt, config, settings):
    """Generate images using the submit_async method."""
    # Prepare the arguments for the API call
    arguments = {
        "prompt": prompt,
        "image_size": settings["image_size"],
        "num_inference_steps": settings["num_inference_steps"],
        "num_images": settings["num_images"],
        "enable_safety_checker": settings["enable_safety_checker"],
    }

    if settings["seed"] is not None:
        arguments["seed"] = settings["seed"]

    # Use the FAL client to generate the images
    handler = await fal_client.submit_async(
        settings["model"],
        arguments=arguments
    )

    log_index = 0
    async for event in handler.iter_events(with_logs=True):
        if isinstance(event, fal_client.InProgress):
            new_logs = event.logs[log_index:]
            for log in new_logs:
                print(log["message"])
            log_index = len(event.logs)

    result = await handler.get()

    # Use timestamp as a unique identifier
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_prompt(prompt, config, settings["seed"], timestamp)

    # Extract and display the image URLs
    if "images" in result:
        async with ClientSession() as session:
            tasks = []
            for idx, image in enumerate(result["images"], start=1):
                print(f"Image {idx}: {image['url']}")
                tasks.append(download_image(session, image['url'], config, settings["seed"], timestamp, idx))
            await asyncio.gather(*tasks)
    else:
        logging.error("No images were generated. Something might have gone wrong.")
        print("No images were generated. Something might have gone wrong.")
    return result


async def _generate_with_subscribe(prompt, config, settings):
    """Generate images using the subscribe method."""

    def on_queue_update(update):
        if isinstance(update, fal_client.InProgress):
            for log in update.logs:
                print(log["message"])

    handler = await fal_client.subscribe_async(
        settings["model"],
        arguments={
            "prompt": prompt
        },
        with_logs=True,
        on_queue_update=on_queue_update,
    )
    result = await handler.get()

    # Use timestamp as a unique identifier
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_prompt(prompt, config, None, timestamp)  # Seed might not be applicable here

    # Extract and display the image URLs
    if "images" in result:
        async with ClientSession() as session:
            tasks = []
            for idx, image in enumerate(result["images"], start=1):
                print(f"Image {idx}: {image['url']}")
                tasks.append(download_image(session, image['url'], config, None, timestamp, idx))
            await asyncio.gather(*tasks)
    else:
        logging.error("No images were generated. Something might have gone wrong.")
        print("No images were generated. Something might have gone wrong.")

    return result


async def generate_images(prompt, config, settings):
    """Generate images based on the provided prompt and settings."""
    try:
        print("\nAlright, let's see what magic we can conjure up with these settings:")
        print(f"Model: {settings['model']}")
        print(f"Prompt: {prompt}")
        if settings['model'] == "fal-ai/flux/schnell":
            print(f"Image Size: {settings['image_size']}")
            print(f"Inference Steps: {settings['num_inference_steps']}")
            print(f"Number of Images: {settings['num_images']}")
            print(f"Safety Checker: {settings['enable_safety_checker']}")
            if settings["seed"] is not None:
                print(f"Seed: {settings['seed']}\n")

        model_function_map = {
            "fal-ai/flux/schnell": _generate_with_submit,
            "fal-ai/flux-pro/kontext/text-to-image": _generate_with_subscribe
        }

        model_function = model_function_map.get(settings["model"])

        if model_function:
            return await model_function(prompt, config, settings)
        else:
            logging.error(f"Unsupported model: {settings['model']}")
            print(f"Sorry, the model '{settings['model']}' is not supported.")
            return None

    except fal_client.auth.MissingCredentialsError as e:
        logging.error(f"Missing API key: {str(e)}")
        print("Uh-oh, looks like you forgot the secret handshake (API key)! Go fetch it and try again.")
        print(str(e))

    except Exception as e:
        logging.error(f"An unexpected error occurred: {str(e)}")
        print("Yikes! Something went wrong, but let's not panic. Here's what happened:")
        print(str(e))


def run_asyncio_task(prompt, config, settings):
    """Run the image generation task asynchronously."""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(generate_images(prompt, config, settings))
    except Exception as e:
        logging.error(f"An unexpected error occurred: {str(e)}")
        print(f"An unexpected error occurred: {str(e)}")