import json
import os

from utils import get_user_input  # Import the utility function

CONFIG_PATH = 'config.json'


def load_config():
    """Load the configuration from the config.json file or create it if it doesn't exist."""
    if not os.path.exists(CONFIG_PATH):
        print("Config file not found. Creating a new one with default settings.")
        config = get_default_config()
        save_config(config)
    else:
        with open(CONFIG_PATH, 'r') as f:
            config = json.load(f)

    # Set the API key as an environment variable if present
    if "api_key" in config and config["api_key"]:
        os.environ["FAL_KEY"] = config["api_key"]

    return config


def save_config(config):
    """Save the configuration to the config.json file."""
    with open(CONFIG_PATH, 'w') as f:
        json.dump(config, f, indent=4)


def get_default_config():
    """Return the default configuration."""
    return {
        "output_directory": "output",
        "image_format": "jpg",
        "retry_attempts": 3,
        "api_key": "",  # Empty by default
        "default_settings": {
            "model": "fal-ai/flux/schnell",
            "image_size": "landscape_4_3",
            "num_inference_steps": 4,
            "num_images": 1,
            "enable_safety_checker": False,
            "seed": None
        }
    }


def ensure_output_directory(config):
    """Ensure the output directory exists."""
    os.makedirs(config["output_directory"], exist_ok=True)


def configure_settings(default_settings):
    """Interactive interface to configure settings."""
    while True:
        print("\nWhich setting would you like to change?")
        print(f"1. Image Size (current: {default_settings['image_size']})")
        print(f"2. Number of Inference Steps (current: {default_settings['num_inference_steps']})")
        print(f"3. Number of Images (current: {default_settings['num_images']})")
        print(f"4. Safety Checker (current: {default_settings['enable_safety_checker']})")
        print(f"5. Seed (current: {default_settings['seed']})")
        print(f"6. Model (current: {default_settings['model']})")
        print("7. Done - Save and Return to Main Menu")

        choice = get_user_input("Select an option to change (or '7' to finish): ")

        if choice == "1":
            default_settings["image_size"] = get_user_input(
                "Choose your image size ('square_hd', 'square', 'portrait_4_3', 'portrait_16_9', 'landscape_4_3', 'landscape_16_9')",
                default_settings["image_size"]
            )
        elif choice == "2":
            default_settings["num_inference_steps"] = int(get_user_input(
                "How many inference steps do you want? (More steps = more detail!)",
                default_settings["num_inference_steps"]
            ))
        elif choice == "3":
            default_settings["num_images"] = int(get_user_input(
                "How many images should I whip up for you?",
                default_settings["num_images"]
            ))
        elif choice == "4":
            default_settings["enable_safety_checker"] = get_user_input(
                "Do you want the safety checker on? (true/false, but I suggest living on the edge!)",
                str(default_settings["enable_safety_checker"]).lower()
            ).lower() == "true"
        elif choice == "5":
            seed = get_user_input("Got a lucky number for the seed? (optional)", default_settings["seed"])
            default_settings["seed"] = int(seed) if seed else None
        elif choice == "6":
            print("Available models:")
            print("1. fal-ai/flux/schnell")
            print("2. fal-ai/flux-pro/kontext/text-to-image")
            model_choice = get_user_input("Select a model", "1")
            if model_choice == "1":
                default_settings["model"] = "fal-ai/flux/schnell"
            elif model_choice == "2":
                default_settings["model"] = "fal-ai/flux-pro/kontext/text-to-image"
        elif choice == "7":
            print("\nSettings updated! Returning to the main menu...\n")
            break
        else:
            print("Hmm, that's not a valid option. Try again!")

    return default_settings
